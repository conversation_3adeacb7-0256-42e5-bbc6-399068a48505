// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model School {
  id         Int      @id @default(autoincrement())
  name       String   @db.Text
  address    String   @db.Text
  city       String   @db.Text
  state      String   @db.Text
  contact    String   @db.VarChar(15)  // phone number
  image      String   @db.Text         // file path
  email_id   String   @unique @db.VarChar(255)

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("schools")
}

