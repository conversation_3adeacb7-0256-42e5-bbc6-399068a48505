'use client'

import { useForm } from 'react-hook-form'
import { useState } from 'react'

type FormData = {
  name: string
  address: string
  city: string
  state: string
  contact: string
  email_id: string
  image: FileList
}

export default function AddSchool() {
  const { register, handleSubmit, formState: { errors }, reset } = useForm<FormData>()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState('')

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true)
    setMessage('')

    const formData = new FormData()
    formData.append('name', data.name)
    formData.append('address', data.address)
    formData.append('city', data.city)
    formData.append('state', data.state)
    formData.append('contact', data.contact)
    formData.append('email_id', data.email_id)
    formData.append('image', data.image[0])

    try {
      const response = await fetch('/api/schools', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        setMessage('School added successfully!')
        reset()
      } else {
        setMessage('Failed to add school')
      }
    } catch (error) {
      setMessage('Error occurred while adding school')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Add School</h1>
        
        <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-6 rounded-lg shadow-md space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">School Name</label>
            <input
              {...register('name', { required: 'School name is required' })}
              className="w-full p-2 border rounded-md"
              placeholder="Enter school name"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Address</label>
            <textarea
              {...register('address', { required: 'Address is required' })}
              className="w-full p-2 border rounded-md"
              rows={3}
              placeholder="Enter full address"
            />
            {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">City</label>
              <input
                {...register('city', { required: 'City is required' })}
                className="w-full p-2 border rounded-md"
                placeholder="Enter city"
              />
              {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">State</label>
              <input
                {...register('state', { required: 'State is required' })}
                className="w-full p-2 border rounded-md"
                placeholder="Enter state"
              />
              {errors.state && <p className="text-red-500 text-sm mt-1">{errors.state.message}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Contact Number</label>
            <input
              {...register('contact', { 
                required: 'Contact number is required',
                pattern: {
                  value: /^[0-9]{10}$/,
                  message: 'Please enter a valid 10-digit phone number'
                }
              })}
              className="w-full p-2 border rounded-md"
              placeholder="Enter 10-digit phone number"
            />
            {errors.contact && <p className="text-red-500 text-sm mt-1">{errors.contact.message}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Email ID</label>
            <input
              type="email"
              {...register('email_id', { 
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Please enter a valid email address'
                }
              })}
              className="w-full p-2 border rounded-md"
              placeholder="Enter email address"
            />
            {errors.email_id && <p className="text-red-500 text-sm mt-1">{errors.email_id.message}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">School Image</label>
            <input
              type="file"
              accept="image/*"
              {...register('image', { required: 'School image is required' })}
              className="w-full p-2 border rounded-md"
            />
            {errors.image && <p className="text-red-500 text-sm mt-1">{errors.image.message}</p>}
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? 'Adding School...' : 'Add School'}
          </button>

          {message && (
            <p className={`text-center ${message.includes('successfully') ? 'text-green-600' : 'text-red-600'}`}>
              {message}
            </p>
          )}
        </form>
      </div>
    </div>
  )
}