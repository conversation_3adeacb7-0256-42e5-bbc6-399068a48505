import Link from 'next/link'
import { NavigationHeader } from '@/components/navigation-header'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { GraduationCap, Plus, Search, Users } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <NavigationHeader />

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-white/10 rounded-full">
                <GraduationCap className="h-16 w-16" />
              </div>
            </div>
            <h1 className="text-5xl font-bold mb-6">School Connect</h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto">
              Your comprehensive platform to discover and manage school information.
              Find the perfect school for your child or contribute to our growing directory.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/showSchools">
                <Button size="lg" className="bg-green-500 hover:bg-green-600 text-white px-8 py-3">
                  <Search className="mr-2 h-5 w-5" />
                  Find Schools
                </Button>
              </Link>
              <Link href="/addSchool">
                <Button size="lg" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20 px-8 py-3">
                  <Plus className="mr-2 h-5 w-5" />
                  Add School
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose School Connect?</h2>
          <p className="text-lg text-gray-600">Everything you need to find and manage school information</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-center p-6 hover:shadow-lg transition-shadow">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Easy Search</h3>
              <p className="text-gray-600">Find schools by name, location, or other criteria with our powerful search and filter system.</p>
            </CardContent>
          </Card>

          <Card className="text-center p-6 hover:shadow-lg transition-shadow">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Add Schools</h3>
              <p className="text-gray-600">Contribute to our directory by adding new schools with detailed information and images.</p>
            </CardContent>
          </Card>

          <Card className="text-center p-6 hover:shadow-lg transition-shadow">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Community Driven</h3>
              <p className="text-gray-600">Built by the community, for the community. Help others find the right school for their children.</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}