'use client'

import { useEffect, useState, useCallback } from 'react'
import { NavigationHeader } from '@/components/navigation-header'
import { SchoolFilters } from '@/components/school-filters'
import { SchoolCard } from '@/components/school-card'
import { Skeleton } from '@/components/ui/skeleton'

type School = {
  id: number
  name: string
  address: string
  city: string
  state: string
  contact: string
  email_id: string
  image: string
}

interface ActiveFilters {
  city?: string
  state?: string
}

export default function ShowSchools() {
  const [schools, setSchools] = useState<School[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({})

  const fetchSchools = useCallback(async (search?: string, filters?: ActiveFilters) => {
    setLoading(true)
    try {
      const params = new URLSearchParams()

      if (search) {
        params.append('search', search)
      }

      if (filters?.city) {
        params.append('city', filters.city)
      }

      if (filters?.state) {
        params.append('state', filters.state)
      }

      const response = await fetch(`/api/schools?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        setSchools(data)
      }
    } catch (error) {
      console.error('Error fetching schools:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchSchools(searchQuery, activeFilters)
  }, [fetchSchools, searchQuery, activeFilters])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  const handleFiltersChange = (filters: ActiveFilters) => {
    setActiveFilters(filters)
  }

  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {[...Array(8)].map((_, i) => (
        <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden">
          <Skeleton className="h-48 w-full" />
          <div className="p-4 space-y-3">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="flex space-x-2">
              <Skeleton className="h-8 flex-1" />
              <Skeleton className="h-8 flex-1" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation Header */}
      <NavigationHeader onSearch={handleSearch} searchQuery={searchQuery} />

      {/* Filters */}
      <SchoolFilters
        onFiltersChange={handleFiltersChange}
        activeFilters={activeFilters}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">School Search</h1>
          <p className="text-lg text-gray-600">Find the right school for your child</p>
          {schools.length > 0 && !loading && (
            <p className="text-sm text-gray-500 mt-2">
              Showing {schools.length} school{schools.length !== 1 ? 's' : ''}
            </p>
          )}
        </div>

        {/* Content */}
        {loading ? (
          <LoadingSkeleton />
        ) : schools.length === 0 ? (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No schools found</h3>
              <p className="text-gray-600 mb-6">
                {searchQuery || Object.keys(activeFilters).length > 0
                  ? "Try adjusting your search or filters to find more schools."
                  : "Be the first to add a school to our directory."}
              </p>
              <a
                href="/addSchool"
                className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors"
              >
                Add School
              </a>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {schools.map((school) => (
              <SchoolCard key={school.id} school={school} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}