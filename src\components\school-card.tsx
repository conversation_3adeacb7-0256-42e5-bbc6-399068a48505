'use client'

import Image from 'next/image'
import { Plus, Star } from 'lucide-react'
import { useState } from 'react'

interface School {
  id: number
  name: string
  address: string
  city: string
  state: string
  contact: string
  email_id: string
  image: string
}

interface SchoolCardProps {
  school: School
}

export function SchoolCard({ school }: SchoolCardProps) {
  const [imageError, setImageError] = useState(false)

  // Generate a random rating for demo purposes (in real app, this would come from database)
  const rating = Math.floor(Math.random() * 2) + 4 // Random rating between 4-5
  const ratingDecimal = Math.floor(Math.random() * 10) // Random decimal

  const handleImageError = () => {
    setImageError(true)
  }

  // Generate board type (ICSE, CBSE, etc.)
  const boardTypes = ['ICSE', 'CBSE', 'State Board', 'IB']
  const boardType = boardTypes[Math.floor(Math.random() * boardTypes.length)]

  return (
    <div className="school-card">
      {/* School Image */}
      <div className="school-image">
        {!imageError ? (
          <Image
            src={school.image}
            alt={school.name}
            fill
            className="object-cover"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-300 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-2xl font-bold text-blue-600">
                  {school.name.charAt(0)}
                </span>
              </div>
              <p className="text-sm text-blue-600 font-medium">No Image</p>
            </div>
          </div>
        )}

        {/* Add Button */}
        <button className="absolute top-3 right-3 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors">
          <Plus className="h-4 w-4 text-gray-600" />
        </button>
      </div>

      {/* Card Content */}
      <div className="p-4">
        {/* Rating and Board */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < rating ? 'fill-yellow-400 text-yellow-400' : 'fill-gray-200 text-gray-200'
                }`}
              />
            ))}
          </div>
          <span className="text-sm font-medium text-cyan-500 bg-cyan-50 px-2 py-1 rounded">
            {boardType}
          </span>
        </div>

        {/* School Location */}
        <div className="text-sm text-cyan-500 font-medium mb-1">
          {school.city}
        </div>

        {/* School Name */}
        <h3 className="font-bold text-lg text-gray-900 mb-2 line-clamp-2 leading-tight">
          {school.name}
        </h3>

        {/* Location */}
        <p className="text-sm text-gray-500 mb-4">
          {school.city}
        </p>

        {/* Apply Now Button */}
        <button className="btn-apply">
          Apply Now
        </button>
      </div>
    </div>
  )
}
