'use client'

import Image from 'next/image'
import { MapPin, Phone, Mail, Star, Heart } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useState } from 'react'

interface School {
  id: number
  name: string
  address: string
  city: string
  state: string
  contact: string
  email_id: string
  image: string
}

interface SchoolCardProps {
  school: School
}

export function SchoolCard({ school }: SchoolCardProps) {
  const [isFavorited, setIsFavorited] = useState(false)
  const [imageError, setImageError] = useState(false)

  // Generate a random rating for demo purposes (in real app, this would come from database)
  const rating = Math.floor(Math.random() * 2) + 4 // Random rating between 4-5
  const reviewCount = Math.floor(Math.random() * 500) + 50 // Random review count

  const handleFavoriteToggle = () => {
    setIsFavorited(!isFavorited)
  }

  const handleImageError = () => {
    setImageError(true)
  }

  return (
    <Card className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white border border-gray-200 overflow-hidden">
      <div className="relative">
        {/* School Image */}
        <div className="relative h-48 w-full overflow-hidden">
          {!imageError ? (
            <Image
              src={school.image}
              alt={school.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
              onError={handleImageError}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-300 rounded-full flex items-center justify-center mx-auto mb-2">
                  <span className="text-2xl font-bold text-purple-600">
                    {school.name.charAt(0)}
                  </span>
                </div>
                <p className="text-sm text-purple-600 font-medium">No Image</p>
              </div>
            </div>
          )}
          
          {/* Favorite Button */}
          <button
            onClick={handleFavoriteToggle}
            className="absolute top-3 right-3 p-2 bg-white/90 hover:bg-white rounded-full shadow-md transition-all duration-200"
          >
            <Heart 
              className={`h-4 w-4 ${isFavorited ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} 
            />
          </button>

          {/* Rating Badge */}
          <div className="absolute top-3 left-3">
            <Badge className="bg-white/90 text-gray-800 hover:bg-white">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
              {rating}.{Math.floor(Math.random() * 10)}
            </Badge>
          </div>
        </div>

        <CardContent className="p-4">
          {/* School Name */}
          <h3 className="font-bold text-lg mb-2 line-clamp-2 text-gray-900 group-hover:text-purple-600 transition-colors">
            {school.name}
          </h3>

          {/* Location */}
          <div className="flex items-start space-x-2 mb-3">
            <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-600 line-clamp-2">
              <p>{school.address}</p>
              <p className="font-medium">{school.city}, {school.state}</p>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">{school.contact}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600 truncate">{school.email_id}</span>
            </div>
          </div>

          {/* Reviews */}
          <div className="flex items-center space-x-1 mb-4">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-4 w-4 ${
                    i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-600">({reviewCount} reviews)</span>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button 
              className="flex-1 bg-green-500 hover:bg-green-600 text-white"
              size="sm"
            >
              Apply Now
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              className="flex-1 border-purple-200 text-purple-600 hover:bg-purple-50"
            >
              View Details
            </Button>
          </div>
        </CardContent>
      </div>
    </Card>
  )
}
